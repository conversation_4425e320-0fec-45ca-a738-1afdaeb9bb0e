<#
INSTRUÇÕES DE EXECUÇÃO:

Para executar este script, você pode usar um dos seguintes métodos:

1. MÉTODO PREFERIDO - Executar uma única vez sem alterar políticas:
   powershell -ExecutionPolicy Bypass -File "CriarAtalho.ps1"

2. Para desbloquear permanentemente este script (executar como administrador):
   Unblock-File -Path "CriarAtalho.ps1"
   Depois você pode executar normalmente com: .\CriarAtalho.ps1

3. Se preferir mudar a política de execução (não recomendado para ambientes corporativos):
   Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned
   Depois você pode executar normalmente com: .\CriarAtalho.ps1

Este script irá:
1. Instalar as dependências do Python listadas em requirements.txt
2. Criar um atalho na área de trabalho para o aplicativo
#>

# Script para criar atalho na área de trabalho
Add-Type -AssemblyName System.Windows.Forms

# Obtém o diretório onde este script está localizado
$scriptDir = $PSScriptRoot

# Instala as dependências do Python
Write-Host "Instalando dependências do Python..."
$requirementsFile = Join-Path $scriptDir "requirements.txt"
if (Test-Path $requirementsFile) {
    try {
        & pip install -r "$requirementsFile"
        Write-Host "Dependências instaladas com sucesso!" -ForegroundColor Green
    } catch {
        Write-Host "Erro ao instalar dependências: $_" -ForegroundColor Red
    }
} else {
    Write-Host "Arquivo requirements.txt não encontrado. Continuando sem instalar dependências..." -ForegroundColor Yellow
}

# Encontra o arquivo Python na mesma pasta do script
$pythonFile = Join-Path $scriptDir "main.py"

$WshShell = New-Object -comObject WScript.Shell
$DesktopPath = [Environment]::GetFolderPath('Desktop')

# Garante que o caminho do Desktop esteja correto
Write-Host "Criando atalho no Desktop: $DesktopPath"

$Shortcut = $WshShell.CreateShortcut("$DesktopPath\Separador de Pedidos Shopee.lnk")

# Usando ícone de pasta de documentos (mais adequado para pedidos)
$IconLocation = [System.IO.Path]::Combine([System.Environment]::SystemDirectory, 'imageres.dll,112')

# Define o destino como o interpretador Python e passa o arquivo .pyw como argumento
# Usando pythonw.exe para evitar a janela do terminal
$pythonwPath = (Get-Command pythonw -ErrorAction SilentlyContinue).Source
if (-not $pythonwPath) {
    $pythonwPath = (Get-Command python -ErrorAction SilentlyContinue).Source -replace 'python.exe$', 'pythonw.exe'
}

if (-not $pythonwPath -or -not (Test-Path $pythonwPath)) {
    Write-Host "pythonw.exe não encontrado. Usando pythonw.exe do sistema..." -ForegroundColor Yellow
    $pythonwPath = "pythonw.exe"
}

$Shortcut.TargetPath = $pythonwPath
$Shortcut.Arguments = "`"$pythonFile`""

# Usa o caminho correto do Desktop detectado pelo sistema
$Shortcut.WorkingDirectory = $DesktopPath

$Shortcut.IconLocation = $IconLocation
$Shortcut.Description = "Abrir Separador de Pedidos Shopee"
$Shortcut.Save()

# Exibe as propriedades do atalho para confirmação
Write-Host "Atalho criado com as seguintes propriedades:"
Write-Host "Destino: $($Shortcut.TargetPath) $($Shortcut.Arguments)"
Write-Host "Iniciar em: $($Shortcut.WorkingDirectory)"

[System.Windows.Forms.MessageBox]::Show('Atalho "Separador de Pedidos Shopee" criado com sucesso na área de trabalho!', 'Sucesso', 'OK', 'Information')
