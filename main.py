#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main application for Shopee Order Processor
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from tkinter.font import Font
import traceback
from pathlib import Path
from datetime import datetime

from utils.file_utils import check_and_install_packages, log_error
from services.order_processor import OrderProcessor
from services.pdf_generator import PDFGenerator

# Cores do tema
COLORS = {
    'primary': '#2c3e50',
    'secondary': '#3498db',
    'success': '#2ecc71',
    'danger': '#e74c3c',
    'light': '#ecf0f1',
    'dark': '#2c3e50',
    'text': '#2c3e50',
    'background': '#f5f6fa'
}

# Estilos
STYLES = {
    'title': ('Segoe UI', 14, 'bold'),  # Reduzido de 16
    'subtitle': ('Segoe UI', 9),  # Reduzido de 10
    'button': ('Segoe UI', 9, 'bold'),  # Reduzido de 10
    'label': ('Segoe UI', 8),  # Reduzido de 9
    'status': ('Segoe UI', 8, 'italic')  # Reduzido de 9
}

class HoverButton(tk.Button):
    """Botão com efeito hover personalizado"""
    def __init__(self, master=None, **kw):
        self.default_bg = kw.pop('bg', COLORS['secondary'])
        self.hover_bg = kw.pop('hover_bg', '#2980b9')
        self.default_fg = kw.pop('fg', 'white')
        super().__init__(master, **kw)
        
        self['bg'] = self.default_bg
        self['fg'] = self.default_fg
        self['borderwidth'] = 0
        self['relief'] = 'flat'
        self['cursor'] = 'hand2'
        
        self.bind('<Enter>', self.on_enter)
        self.bind('<Leave>', self.on_leave)
    
    def on_enter(self, e):
        self['bg'] = self.hover_bg
    
    def on_leave(self, e):
        self['bg'] = self.default_bg

class ShopeeApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Processador de Pedidos Shopee")
        
        # Definir tamanho mínimo e máximo para garantir que todos os elementos sejam visíveis
        min_width = 700  # Reduzido de 800
        min_height = 550  # Reduzido de 650
        
        # Definir tamanho inicial baseado no mínimo necessário
        self.root.minsize(min_width, min_height)
        
        # Configurar o grid principal para expansão
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # Configurar tema e cores
        self.root.configure(bg=COLORS['background'])
        
        # Usar o tamanho mínimo definido
        width = min_width
        height = min_height
        
        # Centralizar a janela na tela
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)
        
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
        # Configurar estilo
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # Configurar fonte padrão
        default_font = Font(family='Segoe UI', size=8)  # Reduzido de 9
        self.root.option_add('*Font', default_font)
        
        # Variables
        self.excel_path = tk.StringVar()
        self.external_pdf_path = tk.StringVar()
        self.status = tk.StringVar(value="Selecione o arquivo Excel e clique em Processar")
        self.separate_pdfs = tk.BooleanVar(value=False)
        
        # Inicializar atributos
        self.processor = None
        self.separation_data = None
        
        # Carregar ícones
        self.load_icons()
        
        self.setup_ui()
    
    def load_icons(self):
        """Carrega os ícones usados na interface"""
        try:
            # Ícones base64 (simplificado para exemplo)
            self.excel_icon = '📊'  # Ícone unicode como fallback
            self.pdf_icon = '📄'    # Ícone unicode como fallback
        except Exception as e:
            # Fallback para texto caso os ícones não carreguem
            self.excel_icon = '[EXCEL]'
            self.pdf_icon = '[PDF]'

    def setup_ui(self):
        """Configura a interface do usuário"""
        # Configurar o gerenciador de layout principal
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # Container principal
        main_container = tk.Frame(self.root, bg=COLORS['background'])
        main_container.grid(row=0, column=0, sticky='nsew', padx=10, pady=10)
        
        # Configurar pesos das linhas e colunas do container principal
        main_container.columnconfigure(0, weight=1)
        main_container.rowconfigure(1, weight=1)  # Linha do conteúdo será expansível
        
        # Frame de cabeçalho
        header_frame = tk.Frame(main_container, bg=COLORS['background'])
        header_frame.grid(row=0, column=0, sticky='ew', pady=(0, 20))
        
        # Título e subtítulo em uma única linha
        title_frame = tk.Frame(header_frame, bg=COLORS['background'])
        title_frame.pack(fill='x', pady=(0, 2))
        
        tk.Label(
            title_frame,
            text="Processador de Pedidos",
            font=STYLES['title'],
            fg=COLORS['primary'],
            bg=COLORS['background']
        ).pack(side='left')
        
        tk.Label(
            title_frame,
            text="Shopee",
            font=STYLES['title'],
            fg=COLORS['secondary'],
            bg=COLORS['background']
        ).pack(side='left', padx=5)
        
        # Frame principal para conteúdo
        content_frame = tk.Frame(main_container, bg=COLORS['background'])
        content_frame.grid(row=1, column=0, sticky='nsew')
        
        # Configurar pesos do frame de conteúdo
        content_frame.columnconfigure(0, weight=1)
        content_frame.rowconfigure(0, weight=0)  # Frame de entrada (não expansível)
        content_frame.rowconfigure(1, weight=1)  # Área de log (expansível - prioridade)
        content_frame.rowconfigure(2, weight=0)  # Botão de processar (não expansível)
        content_frame.rowconfigure(3, weight=0)  # Barra de status (não expansível)
        
        # Frame para os campos de entrada
        input_frame = tk.Frame(content_frame, bg=COLORS['background'])
        input_frame.grid(row=0, column=0, sticky='nsew', pady=(0, 15), padx=5)
        input_frame.columnconfigure(0, weight=1)  # Coluna expansível
        input_frame.rowconfigure(0, weight=0)  # Excel
        input_frame.rowconfigure(1, weight=0)  # PDF
        input_frame.rowconfigure(2, weight=0)  # Opções

        # Excel file selection
        excel_frame = tk.LabelFrame(
            input_frame,
            text=" 1. Arquivo Excel de Pedidos ",
            font=STYLES['label'],
            bg=COLORS['background'],
            fg=COLORS['dark'],
            padx=8,
            pady=6,
            bd=1,
            relief='groove'
        )
        excel_frame.grid(row=0, column=0, sticky='ew', pady=(0, 15), ipady=5)
        excel_frame.columnconfigure(0, weight=1)  # Coluna expansível
        
        # Frame interno para entrada e botão
        excel_inner_frame = tk.Frame(excel_frame, bg=COLORS['background'])
        excel_inner_frame.grid(row=0, column=0, sticky='ew')
        excel_inner_frame.columnconfigure(0, weight=1)  # Campo de entrada expansível
        excel_inner_frame.columnconfigure(1, weight=0)  # Botão não expansível
        
        # Configurar estilo para os campos de entrada
        self.style.configure('TEntry',
                          padding=5,
                          relief='solid',
                          borderwidth=1,
                          fieldbackground='white',
                          foreground=COLORS['dark'])
        
        # Configurar estilo quando o campo estiver em foco
        self.style.map('TEntry',
                     fieldbackground=[('focus', 'white')],
                     foreground=[('focus', COLORS['dark'])])
        
        # Estilo para os campos de entrada
        entry_style = {
            'style': 'TEntry',
            'width': 36
        }
        
        # Frame para o campo de entrada com borda
        excel_entry_frame = tk.Frame(excel_inner_frame, bg='#bdc3c7', bd=1)
        excel_entry_frame.grid(row=0, column=0, sticky='ew', padx=(0, 10))
        excel_entry_frame.columnconfigure(0, weight=1)  # Campo de entrada expansível
        
        # Campo de entrada dentro do frame
        excel_entry = ttk.Entry(
            excel_entry_frame,
            textvariable=self.excel_path,
            **entry_style
        )
        excel_entry.grid(row=0, column=0, sticky='ew', padx=1, pady=1)
        
        # Botão de seleção
        select_btn = HoverButton(
            excel_inner_frame,
            text=f" {self.excel_icon} Selecionar",
            command=self.select_excel,
            bg=COLORS['secondary'],
            fg='white',
            font=STYLES['button'],
            padx=15,
            pady=5
        )
        select_btn.grid(row=0, column=1, sticky='e')
        
        # Tooltip
        self.create_tooltip(
            excel_inner_frame,
            "Selecione o arquivo Excel com os pedidos da Shopee"
        )

        # External PDF selection
        pdf_frame = tk.LabelFrame(
            input_frame,
            text=" 2. PDF Externo (Opcional) ",
            font=STYLES['label'],
            bg=COLORS['background'],
            fg=COLORS['dark'],
            padx=8,
            pady=6,
            bd=1,
            relief='groove'
        )
        pdf_frame.grid(row=1, column=0, sticky='ew', pady=(0, 15), ipady=5)
        pdf_frame.columnconfigure(0, weight=1)  # Coluna expansível
        
        # Frame interno para entrada e botão
        pdf_inner_frame = tk.Frame(pdf_frame, bg=COLORS['background'])
        pdf_inner_frame.grid(row=0, column=0, sticky='ew')
        pdf_inner_frame.columnconfigure(0, weight=1)  # Campo de entrada expansível
        pdf_inner_frame.columnconfigure(1, weight=0)  # Botão não expansível
        
        # Campo de entrada
        # Frame para o campo de entrada com borda
        pdf_entry_frame = tk.Frame(pdf_inner_frame, bg='#bdc3c7', bd=1)
        pdf_entry_frame.grid(row=0, column=0, sticky='ew', padx=(0, 10))
        pdf_entry_frame.columnconfigure(0, weight=1)  # Campo de entrada expansível
        
        # Campo de entrada dentro do frame
        pdf_entry = ttk.Entry(
            pdf_entry_frame,
            textvariable=self.external_pdf_path,
            **entry_style
        )
        pdf_entry.grid(row=0, column=0, sticky='ew', padx=1, pady=1)
        
        # Botão de seleção
        pdf_btn = HoverButton(
            pdf_inner_frame,
            text=f" {self.pdf_icon} Selecionar",
            command=self.select_pdf,
            bg='#7f8c8d',
            fg='white',
            font=STYLES['button'],
            padx=15,
            pady=5
        )
        pdf_btn.grid(row=0, column=1, sticky='e')
        
        # Tooltip
        self.create_tooltip(
            pdf_inner_frame,
            "Selecione um PDF adicional para incluir nos relatórios (opcional)"
        )

        # Options
        options_frame = tk.LabelFrame(
            input_frame,
            text=" Opções ",
            font=STYLES['label'],
            bg=COLORS['background'],
            fg=COLORS['dark'],
            padx=8,
            pady=6,
            bd=1,
            relief='groove'
        )
        options_frame.grid(row=2, column=0, sticky='ew', pady=(0, 10), ipady=5)
        options_frame.columnconfigure(0, weight=1)
        
        # Checkbox com estilo personalizado
        check_style = {
            'bg': COLORS['background'],
            'font': STYLES['label'],
            'selectcolor': COLORS['background'],
            'activebackground': COLORS['background'],
            'activeforeground': COLORS['dark']
        }
        
        # Checkbox para opção de separação
        tk.Checkbutton(
            options_frame,
            text="Separar PDFs com 1-2 pedidos na última página",
            variable=self.separate_pdfs,
            **check_style
        ).grid(row=0, column=0, sticky='w', pady=1, padx=5)
        
        # Checkbox para gerar relatório A4
        self.generate_a4 = tk.BooleanVar(value=True)
        tk.Checkbutton(
            options_frame,
            text="Gerar Relatório de Separação A4",
            variable=self.generate_a4,
            **check_style
        ).grid(row=1, column=0, sticky='w', pady=1, padx=5)
        
        # Checkbox para gerar relatório A6
        self.generate_a6 = tk.BooleanVar(value=True)
        tk.Checkbutton(
            options_frame,
            text="Gerar Relatório de Separação A6",
            variable=self.generate_a6,
            **check_style
        ).grid(row=2, column=0, sticky='w', pady=1, padx=5)
        
        # Área de log
        log_frame = tk.Frame(content_frame, bg=COLORS['background'])
        log_frame.grid(row=1, column=0, sticky='nsew', pady=(5, 0), padx=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(1, weight=1)  # Área de texto expansível
        
        # Título da área de log
        tk.Label(
            log_frame,
            text="Log de Atividades:",
            font=('Segoe UI', 8, 'bold'),
            bg=COLORS['background'],
            fg=COLORS['dark']
        ).grid(row=0, column=0, sticky='w', pady=(0, 2))
        
        # Área de texto para o log
        log_container = tk.Frame(log_frame, bg='white', bd=1, relief='solid')
        log_container.grid(row=1, column=0, sticky='nsew')
        
        # Configurar pesos para o container do log
        log_container.columnconfigure(0, weight=1)
        log_container.rowconfigure(0, weight=1)
        
        # Configurar scrollbar
        scrollbar = tk.Scrollbar(log_container)
        scrollbar.grid(row=0, column=1, sticky='ns')
        
        # Configurar área de texto
        self.log_text = tk.Text(
            log_container,
            height=6,
            wrap=tk.WORD,
            font=('Segoe UI', 8),
            bg='white',
            fg=COLORS['dark'],
            padx=5,
            pady=5,
            relief='flat',
            state='disabled',
            yscrollcommand=scrollbar.set
        )
        self.log_text.grid(row=0, column=0, sticky='nsew')
        scrollbar.config(command=self.log_text.yview)
        
        # Botão de processar e status
        process_frame = tk.Frame(content_frame, bg=COLORS['background'])
        process_frame.grid(row=2, column=0, sticky='ew', pady=(8, 5), padx=20)
        process_frame.columnconfigure(0, weight=1)
        
        # Status icon
        self.status_icon = tk.Label(
            process_frame,
            text="",
            font=('Segoe UI', 12),
            bg=COLORS['background'],
            fg=COLORS['dark']
        )
        self.status_icon.grid(row=0, column=1, padx=(10, 0))
        
        self.process_btn = HoverButton(
            process_frame,
            text="🔄 Processar Pedidos",
            command=self.process_orders,
            bg=COLORS['success'],
            fg='white',
            font=('Segoe UI', 9, 'bold'),
            padx=10,
            pady=4,
            width=16
        )
        self.process_btn.grid(row=0, column=0, sticky='nsew')
        
        # Configurar o grid para expansão
        main_container.columnconfigure(0, weight=1)
        main_container.rowconfigure(1, weight=1)
        content_frame.columnconfigure(0, weight=1)
    
    def create_tooltip(self, widget, text):
        """Cria uma dica de ferramenta para o widget"""
        def enter(event):
            x, y, _, _ = widget.bbox("insert")
            x += widget.winfo_rootx() + 25
            y += widget.winfo_rooty() + 25
            
            self.tooltip = tk.Toplevel(widget)
            self.tooltip.wm_overrideredirect(True)
            self.tooltip.wm_geometry(f"+{x}+{y}")
            
            label = tk.Label(
                self.tooltip,
                text=text,
                justify='left',
                background='#ffffe0',
                relief='solid',
                borderwidth=1,
                font=('Segoe UI', 8),
                wraplength=200
            )
            label.pack(ipadx=5, ipady=2)
        
        def leave(event):
            if hasattr(self, 'tooltip'):
                self.tooltip.destroy()
        
        widget.bind('<Enter>', enter)
        widget.bind('<Leave>', leave)

    def select_excel(self):
        """Handle Excel file selection"""
        filename = filedialog.askopenfilename(
            title="Selecione a planilha Excel",
            filetypes=[
                ("Arquivos Excel", "*.xlsx *.xls"),
                ("Todos os arquivos", "*.*")
            ],
        )
        if filename:
            self.excel_path.set(filename)
            msg = f"Arquivo selecionado: {os.path.basename(filename)}"
            self.update_log(msg, clear=True)
            self.update_status_icon("✅")

    def select_pdf(self):
        """Handle PDF file selection"""
        filename = filedialog.askopenfilename(
            title="Selecione o PDF Externo (Opcional)",
            filetypes=[
                ("Arquivos PDF", "*.pdf"),
                ("Todos os arquivos", "*.*")
            ],
        )
        if filename:
            self.external_pdf_path.set(filename)
            msg = f"PDF externo selecionado: {os.path.basename(filename)}"
            self.update_log(msg, clear=False)
            self.update_status_icon("✅")

    def update_status_icon(self, icon):
        """Atualiza o ícone de status"""
        self.status_icon.config(text=icon)
        
    def update_log(self, message, clear=False):
        """Atualiza o log de saída com uma única linha de texto"""
        try:
            # Remove quebras de linha e limita o tamanho do texto
            single_line = ' '.join(str(message).splitlines())
            
            self.log_text.config(state='normal')
            if clear:
                self.log_text.delete(1.0, tk.END)
            
            self.log_text.insert(tk.END, single_line + '\n')
            self.log_text.see(tk.END)  # Rola para o final
            self.log_text.config(state='disabled')
            self.status.set(single_line[:100])  # Atualiza a barra de status (limita a 100 caracteres)
        except Exception as e:
            print(f"Erro ao atualizar o log: {str(e)}")
    
    def copy_log_to_clipboard(self):
        """Copia o conteúdo do log para a área de transferência"""
        try:
            self.root.clipboard_clear()
            self.root.clipboard_append(self.log_text.get(1.0, tk.END))
            self.status.set("Log copiado para a área de transferência!")
            self.root.after(2000, lambda: self.status.set(""))  # Limpa a mensagem após 2 segundos
        except Exception as e:
            self.status.set(f"Erro ao copiar: {str(e)}")
    
    def _generate_orders_pdf(self, orders_file: str, separated_orders_file: str):
        """Gera o PDF de pedidos em segundo plano"""
        try:
            if not hasattr(self, 'processor') or not self.processor.orders:
                return
                
            self.update_log("⏳ Gerando relatório de pedidos em segundo plano...", clear=False)
            # Gerar relatórios com base nas opções selecionadas
            separation_file, orders_file, separated_orders_file = self.processor.generate_filenames()
            generated_files = []
            
            # Gerar relatórios conforme selecionado
            pdf_generator = PDFGenerator()
            
            # Gerar relatório A4 se selecionado
            if self.generate_a4.get():
                pdf_generator.generate_separation_report(
                    self.separation_data,
                    separation_file,
                    format_a6=False
                )
                generated_files.append(f"📄 Relatório A4: {os.path.basename(separation_file)}")
            
            # Gerar relatório A6 se selecionado
            if self.generate_a6.get():
                a6_file = separation_file.replace('.pdf', '_a6.pdf')
                pdf_generator.generate_separation_report(
                    self.separation_data,
                    a6_file,
                    format_a6=True
                )
                generated_files.append(f"📄 Relatório A6: {os.path.basename(a6_file)}")
            
            # Gerar relatório de pedidos (sempre gera o A4, opcionalmente separa os PDFs)
            external_pdf = self.external_pdf_path.get() if self.external_pdf_path.get() else None
            
            # Primeiro, gerar o PDF dos pedidos normalmente
            pdf_generator.generate_orders_pdf(
                self.processor.orders,
                orders_file,
                external_pdf,
                self.separate_pdfs.get()
            )
            
            generated_files.append(f"📄 Relatório de pedidos: {os.path.basename(orders_file)}")
            
            # Se a opção de separar PDFs estiver ativada, adicionar à lista de arquivos gerados
            if self.separate_pdfs.get():
                generated_files.append(f"📄 Relatório de pedidos separados: {os.path.basename(separated_orders_file)}")
            
            # Se o relatório A6 estiver ativado, gerá-lo separadamente
            if self.generate_a6.get():
                try:
                    # Usar o mesmo método que o botão de relatório A6 usa
                    self.print_separation_report(use_a6=True)
                    # Adicionar à lista de arquivos gerados
                    separation_file = separation_file.replace('.pdf', '_a6.pdf')
                    generated_files.append(f"📄 Relatório de separação A6: {os.path.basename(separation_file)}")
                except Exception as e:
                    error_msg = f"\n⚠️ Aviso: Erro ao gerar relatório de separação A6: {str(e)}"
                    self.update_log(error_msg, clear=False)
                    log_error(e, str(e))
            
            # Atualizar interface com os arquivos gerados
            success_msg = f"✅ Processamento concluído com sucesso!\n" + "\n".join(generated_files)
            self.update_log(success_msg, clear=False)
                
        except Exception as e:
            error_msg = f"\n⚠️ Aviso: Erro ao gerar relatório de pedidos: {str(e)}"
            self.update_log(error_msg, clear=False)
            log_error(e, str(e))
    
    def print_separation_report(self, use_a6: bool):
        """Gera e abre o relatório de separação no formato especificado"""
        try:
            if not hasattr(self, 'processor') or not hasattr(self, 'separation_data'):
                self.update_log("❌ Erro: Nenhum dado de relatório disponível. Processe o arquivo primeiro.")
                self.update_status_icon("❌")
                return
                
            self.update_log(f"⏳ Gerando relatório de separação ({'A6' if use_a6 else 'A4'})...", clear=True)
            self.update_status_icon("⏳")
            self.root.update()
            
            # Usar o mesmo padrão do process_orders para gerar os nomes dos arquivos
            separation_file, orders_file, separated_orders_file = self.processor.generate_filenames()
            
            # Ajustar o nome do arquivo para incluir o sufixo do formato
            base_name = os.path.splitext(separation_file)[0]
            format_suffix = '_a6' if use_a6 else '_a4'
            separation_file = f"{base_name}{format_suffix}.pdf"
            
            # Criar um pedido fake com os dados de separação
            from models.order import Order, Product
            
            # Converter os dados de separação para produtos
            products = []
            for row in self.separation_data[1:]:  # Pular o cabeçalho
                if len(row) >= 4:  # Garantir que temos todas as colunas necessárias
                    sku = row[0]
                    try:
                        quantity = int(row[1])
                    except (ValueError, IndexError):
                        quantity = 0
                    name = row[2] if len(row) > 2 else ""
                    variation = row[3] if len(row) > 3 else ""
                    
                    if sku:  # Apenas adicionar produtos com SKU válido
                        products.append(Product(
                            sku=sku,
                            name=name,
                            variation=variation,
                            quantity=quantity
                        ))
            
            # Criar um pedido fake com os produtos
            fake_order = Order(
                order_sn=f"SEP-{datetime.today().strftime('%d%m%y-%H%M')}",
                products=products
            )
            
            # Usar o gerador de PDF existente para gerar o relatório
            pdf_generator = PDFGenerator()
            
            if use_a6:
                # Atualizar o order_sn para o formato desejado no título do A6
                data_hora_titulo_a6 = datetime.today().strftime('%d/%m/%Y %H:%M')
                fake_order.order_sn = f"Separação - {data_hora_titulo_a6}"
                
                # Usar o fluxo de geração de pedidos normal para A6
                pdf_generator.generate_orders_pdf(
                    {"SEPARACAO": fake_order},  # Usar um dicionário com um único pedido
                    separation_file,
                    None,  # Sem PDF externo
                    False  # Não separar última página
                )
            else:
                # Usar o fluxo de relatório de separação para A4
                separation_data = [
                    ["SKU", "QNT", "NOME", "VARIAÇÃO"]  # Cabeçalho
                ]
                for product in products:
                    separation_data.append([
                        product.sku,
                        str(product.quantity),
                        product.name,
                        product.variation
                    ])
                
                pdf_generator.generate_separation_report(
                    separation_data,
                    separation_file,
                    format_a6=False  # Forçar A4
                )
            
            # Verificar se o arquivo foi criado
            if os.path.exists(separation_file):
                success_msg = f"✅ Relatório de separação ({'A6' if use_a6 else 'A4'}) gerado com sucesso!\n   📄 {os.path.basename(separation_file)}"
                self.update_log(success_msg)
                self.update_status_icon("✅")
            else:
                raise FileNotFoundError(f"O arquivo de relatório não foi gerado em: {separation_file}")
                
        except Exception as e:
            error_msg = f"❌ Erro ao gerar relatório de separação: {str(e)}"
            self.update_log(error_msg)
            self.update_status_icon("❌")
            log_error(e, str(e))
    
    def process_orders(self):
        """Processa os pedidos e gera os PDFs"""
        try:
            # Validar entrada
            if not self.excel_path.get():
                error_msg = "❌ Erro: Selecione um arquivo Excel para processar."
                self.update_log(error_msg, clear=True)
                self.update_status_icon("❌")
                return

            self.update_log("⏳ Iniciando processamento do arquivo Excel...", clear=True)
            self.update_status_icon("⏳")
            self.root.update()

            # Inicializar processador e carregar dados
            try:
                self.processor = OrderProcessor(self.excel_path.get())
                self.processor.load_orders()
                self.processor.process_orders()
                
                # Obter caminhos de saída
                separation_file, orders_file, separated_orders_file = self.processor.generate_filenames()
                
                # Obter dados para o relatório de separação
                self.status.set("⏳ Preparando relatório de separação...")
                self.root.update()
                
                self.separation_data = self.processor.get_separation_report_data()
                if not self.separation_data or len(self.separation_data) <= 1:
                    error_msg = "❌ Erro: Dados insuficientes no arquivo Excel"
                    self.update_log(error_msg)
                    self.update_status_icon("❌")
                    return
                
                # Mostrar mensagem de sucesso
                success_msg = "✅ Processamento concluído com sucesso!\n" \
                             "📄 Os relatórios serão gerados automaticamente conforme as opções selecionadas."
                self.update_log(success_msg)
                self.update_status_icon("✅")
                
                # Gerar PDF de pedidos em segundo plano
                self.root.after(100, lambda: self._generate_orders_pdf(orders_file, separated_orders_file))
                    
            except Exception as e:
                error_msg = f"❌ Erro ao processar o arquivo Excel: {str(e)}"
                self.update_log(error_msg)
                self.update_status_icon("❌")
                raise

        except Exception as e:
            error_details = traceback.format_exc()
            error_msg = f"❌ Ocorreu um erro inesperado: {str(e)}\n\nDetalhes:\n{error_details}"
            self.update_log(error_msg)
            self.update_status_icon("❌")
            log_error(e, error_details)
            
            # Mostrar mensagem de erro em uma caixa de diálogo
            messagebox.showerror(
                "Erro",
                f"Ocorreu um erro durante o processamento:\n\n{str(e)}\n\n"
                "Verifique o log para mais detalhes."
            )

def main():
    # Check dependencies
    if not check_and_install_packages():
        sys.exit(1)

    # Create and run application
    root = tk.Tk()
    app = ShopeeApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
