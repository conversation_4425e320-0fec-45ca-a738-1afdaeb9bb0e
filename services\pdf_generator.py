#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PDF generation service for Shopee orders
"""

import io
import os
from typing import List, Tuple, Dict, Optional
from datetime import datetime
import fitz
from PIL import Image
from reportlab.lib.pagesizes import A4, landscape
from reportlab.platypus import SimpleDocT<PERSON><PERSON>, PageBreak, Paragraph, Table, Frame
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfgen import canvas
from reportlab.lib.utils import ImageReader

from config import Config
from models.order import Order
from services.table_formatter import TableFormatter

class PDFGenerator:
    def __init__(self):
        self.a4_width, self.a4_height = A4
        self.config = Config()
        self.margins = self.config.PDF_MARGINS

    def generate_separation_report(self, data: List[List[str]], output_path: str, format_a6: bool = False) -> None:
        """Generate separation report PDF
        
        Args:
            data: List of lists containing the report data
            output_path: Filename for the output PDF (will be saved in current directory)
            format_a6: If True, generates report in A6 format (1/4 page), otherwise uses full page
        """
        if format_a6:
            self._generate_separation_report_a6(data, output_path)
        else:
            self._generate_separation_report_a4(data, output_path)
    
    def _generate_separation_report_a4(self, data: List[List[str]], output_path: str) -> None:
        """Generate A4 format separation report"""
        # Ensure output path is in the current working directory
        filename = os.path.basename(output_path)
        output_path = os.path.join(os.getcwd(), filename)
        
        # Margens mínimas para garantir que a impressão não corte o conteúdo
        margins = {
            'top': 5,
            'bottom': 5,
            'left': 5,
            'right': 5
        }
        
        pdf = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            topMargin=margins['top'],
            leftMargin=margins['left'],
            rightMargin=margins['right'],
            bottomMargin=margins['bottom']
        )
        elements = []

        # Add title with smaller font
        data_atual = datetime.today().strftime('%d/%m/%Y %H:%M')
        titulo = f"Separação Shopee {data_atual}"
        styles = getSampleStyleSheet()
        titulo_style = ParagraphStyle(
            'TituloRelatorio',
            parent=styles['Heading1'],
            fontSize=10,  # Fonte ainda menor para o título
            spaceAfter=4,  # Espaço reduzido após o título
            leading=10     # Altura da linha reduzida
        )
        elements.append(Paragraph(titulo, titulo_style))

        # Create tables for each page (90 rows per page com fonte menor)
        formatter = TableFormatter(A4, is_relatorio_separacao=True)
        for i in range(0, len(data), 90):  # Aumentar para 90 linhas por página
            table_data = data[i:i + 90]
            table = formatter.create_table(
                table_data,
                line_height_multiplier=1.0,  # Espaçamento mínimo entre linhas
                max_font_size=8,   # Tamanho máximo de fonte reduzido
                min_font_size=5    # Tamanho mínimo de fonte reduzido
            )
            elements.append(table)


            if i + 90 < len(data):
                elements.append(PageBreak())

        pdf.build(elements)
    
    def _generate_separation_report_a6(self, data: List[List[str]], output_path: str) -> None:
        """Generate A6 format separation report (1/4 page)"""
        # Ensure output path is in the current working directory
        filename = os.path.basename(output_path)
        output_path = os.path.join(os.getcwd(), filename)
        
        # Create a temporary PDF in memory
        buffer = io.BytesIO()
        
        # Create PDF document with A4 size (will be split into 4 A6 pages)
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            leftMargin=0,
            rightMargin=0,
            topMargin=0,
            bottomMargin=0
        )
        
        # Create a list to hold all elements
        elements = []
        
        # Get styles
        styles = getSampleStyleSheet()
        titulo_style = ParagraphStyle(
            'A6Title',
            parent=styles['Heading3'],
            fontSize=9,
            spaceAfter=6,
            alignment=1  # Center aligned
        )
        
        # Convert data to A6 format (skip header row)
        a6_data = []
        for row in data[1:]:  # Skip header
            a6_data.append([
                row[0],  # SKU
                row[1],  # QNT
                row[2],  # NOME
                row[3]   # VARIAÇÃO
            ])
        
        # Create A6 pages (4 per A4 sheet)
        items_per_page = 8  # Number of items per A6 page
        total_items = len(a6_data)
        
        for i in range(0, total_items, items_per_page):
            # Create a frame for each A6 page
            frame = Frame(
                doc.leftMargin,
                doc.bottomMargin,
                doc.width / 2,
                doc.height / 2,
                leftPadding=10,
                bottomPadding=10,
                rightPadding=10,
                topPadding=10,
                showBoundary=0  # No border
            )
            
            # Create a story for this A6 page
            story = []
            
            # Add title
            data_atual = datetime.today().strftime('%d/%m/%Y %H:%M')
            titulo = f"Separação Shopee\n{data_atual}"
            story.append(Paragraph(titulo, titulo_style))
            
            # Add table with items for this A6 page
            page_data = a6_data[i:i + items_per_page]
            if not page_data:
                continue
                
            # Create table
            table_style = [
                ('FONT', (0, 0), (-1, -1), 'Helvetica', 6),
                ('FONTSIZE', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 2),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 2),
                ('LEFTPADDING', (0, 0), (-1, -1), 2),
                ('RIGHTPADDING', (0, 0), (-1, -1), 2),
                ('GRID', (0, 0), (-1, -1), 0.5, '#cccccc')
            ]
            
            # Calculate column widths (adjust as needed)
            col_widths = [doc.width * 0.15, doc.width * 0.1, doc.width * 0.5, doc.width * 0.25]
            
            # Create table
            table = Table([['SKU', 'QNT', 'NOME', 'VARIAÇÃO']] + page_data, 
                         colWidths=col_widths)
            table.setStyle(table_style)
            story.append(table)
            
            # Add page break if not the last item
            if i + items_per_page < total_items:
                story.append(PageBreak())
            
            # Add this A6 page to the main story
            elements.extend(story)
        
        # Build the PDF with multiple A6 pages per A4 sheet
        doc.build(elements)
        
        # Save the PDF to the output path
        with open(output_path, 'wb') as f:
            f.write(buffer.getvalue())

    def _should_separate_last_orders(self, order_buffers: List[Tuple[io.BytesIO, str]]) -> Tuple[bool, int]:
        """
        Determine if the last 1-2 orders should be separated.
        Returns a tuple of (should_separate, orders_on_last_page)
        """
        total_orders = len(order_buffers)
        orders_on_last_page = total_orders % 4
        return orders_on_last_page in [1, 2], orders_on_last_page

    def generate_orders_pdf(self, orders: Dict[str, Order], output_path: str,
                      external_pdf: Optional[str] = None, separate_last: bool = False) -> None:
        """
        Generate orders PDF with optional external PDF integration.
        
        Args:
            orders: Dictionary of Order objects to include in the PDF
            output_path: Path where the main PDF will be saved (filename only, will be saved in current directory)
            external_pdf: Optional path to an external PDF to include
            separate_last: If True, the last 1-2 orders will be moved to a separate PDF
        """
        # Ensure output path is in the current working directory
        filename = os.path.basename(output_path)
        output_path = os.path.join(os.getcwd(), filename)
        
        # Generate PDF buffers for each order
        order_buffers = self._generate_order_buffers(orders)
        
        # Check if we should separate the last orders
        should_separate, orders_on_last_page = self._should_separate_last_orders(order_buffers)
        
        if external_pdf:
            if separate_last and should_separate:
                # Handle separation with external PDF
                self._generate_combined_pdf_without_last(order_buffers, output_path, external_pdf)
            else:
                # Generate combined PDF with external PDF (all pages, no separation)
                self._generate_combined_pdf(order_buffers, output_path, external_pdf)
        else:
            # Generate orders PDF only (no external PDF)
            if separate_last and should_separate:
                # Separate the last 1-2 orders
                main_order_buffers = order_buffers[:-orders_on_last_page]
                separated_order_buffers = order_buffers[-orders_on_last_page:]
                
                # Generate main PDF with all but the last 1-2 orders
                self._generate_orders_pdf(main_order_buffers, output_path, False)
                
                # Generate separated PDF with the last 1-2 orders
                separated_filename = os.path.basename(output_path).replace('.pdf', '-separados.pdf')
                separated_path = os.path.join(os.getcwd(), separated_filename)
                self._generate_orders_pdf(separated_order_buffers, separated_path, False)
            else:
                # Generate all orders in a single PDF (no separation)
                self._generate_orders_pdf(order_buffers, output_path, False)

    def _calculate_content_size(self, elements_to_measure: List, page_size_for_measurement: Tuple[float, float]) -> Tuple[float, float]:
        """
        Calculate the actual width and height the content elements would occupy.
        Returns (content_block_width, content_block_height).
        These are compared against the available drawing area of the page.
        Excludes PageBreak elements from measurement.
        """
        max_element_width = 0
        total_elements_height = 0
        
        # Create a temporary buffer and canvas to measure elements
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=page_size_for_measurement,
            leftMargin=0,
            rightMargin=0,
            topMargin=0,
            bottomMargin=0
        )
        
        # Create a frame with no margins for accurate measurement
        frame = doc.build([])
        
        for element in elements_to_measure:
            if isinstance(element, PageBreak):
                continue
                
            if hasattr(element, 'wrap'):
                # Get the available width (full page width minus margins)
                available_width = page_size_for_measurement[0] - self.margins['left'] - self.margins['right']
                available_height = page_size_for_measurement[1] - self.margins['top'] - self.margins['bottom']
                
                # Wrap the element to get its dimensions
                w, h = element.wrap(available_width, available_height)
                
                # For tables, we need to consider the actual column widths
                if isinstance(element, Table):
                    # Get the actual width of the table (sum of column widths)
                    w = sum(element._colWidths) if hasattr(element, '_colWidths') else w
                    
                    # Calculate the height based on row heights
                    if hasattr(element, '_rowHeights') and element._rowHeights:
                        h = sum(element._rowHeights)
                
                max_element_width = max(max_element_width, w)
                total_elements_height += h
                
                # Add some spacing between elements (approximation)
                if total_elements_height > 0:  # Don't add spacing before first element
                    total_elements_height += 10  # 10pt spacing between elements
        
        # Add document margins to the total dimensions
        total_width = min(max_element_width + self.margins['left'] + self.margins['right'], page_size_for_measurement[0])
        total_height = min(total_elements_height + self.margins['top'] + self.margins['bottom'], page_size_for_measurement[1])
        
        return total_width, total_height

    def _generate_order_elements(self, order_sn: str, order: Order, page_size: Tuple[float, float], 
                             titulo_style: ParagraphStyle, font_size: int = None) -> Tuple[List, int]:
        """
        Generate PDF elements for an order with specified font size.
        Returns tuple of (elements, used_font_size)
        """
        elements = []
        elements.append(Paragraph(f"Código do Pedido: {order_sn}", titulo_style))
        
        formatter = TableFormatter(page_size, is_relatorio_separacao=False)
        
        # If font_size is specified, use it for both min and max to force that specific size
        if font_size is not None:
            table = formatter.create_table(
                order.to_table_data(),
                line_height_multiplier=self.config.TABLE_STYLES['line_height_multiplier'],
                max_font_size=font_size,
                min_font_size=font_size
            )
            used_font = font_size
        else:
            # Let table formatter choose the best size within min/max range
            table = formatter.create_table(
                order.to_table_data(),
                line_height_multiplier=self.config.TABLE_STYLES['line_height_multiplier'],
                max_font_size=self.config.TABLE_STYLES['max_font_size'],
                min_font_size=self.config.TABLE_STYLES['min_font_size']
            )
            # For this case, we don't know the exact font size used, so return max
            used_font = self.config.TABLE_STYLES['max_font_size']
        
        elements.append(table)
        elements.append(PageBreak())
        return elements, used_font

    def _try_fit_order(self, order_sn: str, order: Order, titulo_style: ParagraphStyle) -> Tuple[io.BytesIO, str]:
        """
        Try to fit an order by adjusting font size and orientation.
        Returns tuple of (buffer, orientation)
        """
        # Safety margin in points
        SAFETY_MARGIN = 5  # Reduced safety margin to allow better fitting
        
        # Define font sizes to try, from largest to smallest
        max_font = min(18, self.config.TABLE_STYLES['max_font_size'])  # Cap at 18pt
        min_landscape_font = 14  # Minimum font size for landscape
        min_portrait_font = 10   # Minimum font size for portrait
        
        # Try both orientations: landscape first, then portrait if needed
        orientations = [
            ('landscape', landscape(A4), min_landscape_font),
            ('portrait', A4, min_portrait_font)
        ]
        
        # Create a range of font sizes to try, from max to min with decreasing steps
        def get_font_sizes(max_size, min_size):
            font_sizes = []
            # First try larger steps for faster finding of approximate size
            if max_size > 12:
                font_sizes.extend(range(max_size, max(min_size, 11) - 1, -2))  # e.g., 18, 16, 14, 12
            # Then try smaller steps for fine-tuning
            font_sizes.extend(range(min(max_size, 12), min_size - 1, -1))  # e.g., 12, 11, 10, ...
            return font_sizes
        
        # Debug info
        print(f"\nTrying to fit order {order_sn} (products: {len(order.products)})")
        
        # Try each orientation with its specific minimum font size
        for orientation_name, page_size, min_font in orientations:
            drawable_width = page_size[0] - self.margins['left'] - self.margins['right'] - (2 * SAFETY_MARGIN)
            drawable_height = page_size[1] - self.margins['top'] - self.margins['bottom'] - (2 * SAFETY_MARGIN)
            
            print(f"\nTrying {orientation_name} orientation (min font: {min_font}pt)...")
            
            # Get font sizes to try for this orientation
            font_sizes = get_font_sizes(max_font, min_font)
            
            # Try different font sizes
            for font_size in font_sizes:
                elements, _ = self._generate_order_elements(
                    order_sn, order, page_size, titulo_style, font_size=font_size
                )
                
                # Filter out PageBreak elements for size calculation
                content_elements = [el for el in elements if not isinstance(el, PageBreak)]
                
                # Calculate content size
                content_width, content_height = self._calculate_content_size(content_elements, page_size)
                
                # Calculate required scaling to fit
                width_scale = min(1.0, drawable_width / (content_width + 1e-6))  # Avoid division by zero
                height_scale = min(1.0, drawable_height / (content_height + 1e-6))
                fits = width_scale >= 0.95 and height_scale >= 0.95  # Allow 5% tolerance
                
                print(f"  Font: {font_size:2d}pt - "
                      f"Content: {content_width:.1f}x{content_height:.1f} "
                      f"(max: {drawable_width:.1f}x{drawable_height:.1f}) - "
                      f"Scale: {min(width_scale, height_scale):.1%} - "
                      f"Fits: {'✓' if fits else '✗'}")
                
                if fits:
                    print(f"✓ Fits in {orientation_name} with font size {font_size}pt")
                    buffer = io.BytesIO()
                    try:
                        pdf = SimpleDocTemplate(
                            buffer,
                            pagesize=page_size,
                            topMargin=self.margins['top'],
                            leftMargin=self.margins['left'],
                            rightMargin=self.margins['right'],
                            bottomMargin=self.margins['bottom']
                        )
                        pdf.build(elements)
                        buffer.seek(0)
                        return buffer, orientation_name
                    except Exception as e:
                        print(f"Error generating PDF: {e}")
                        continue
        
        # If we get here, no fit was found - use minimum font size in portrait as fallback
        print("No fit found, using minimum font size in portrait as fallback...")
        portrait_min_font = min_portrait_font  # Use the minimum portrait font size
        elements, _ = self._generate_order_elements(
            order_sn, order, A4, titulo_style, font_size=portrait_min_font
        )
        buffer = io.BytesIO()
        try:
            pdf = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                topMargin=self.margins['top'],
                leftMargin=self.margins['left'],
                rightMargin=self.margins['right'],
                bottomMargin=self.margins['bottom']
            )
            pdf.build(elements)
            buffer.seek(0)
            return buffer, 'portrait'
        except Exception as e:
            print(f"Error generating fallback PDF: {e}")
            raise RuntimeError("Failed to generate PDF with minimum font size in portrait")

    def _generate_order_buffers(self, orders: Dict[str, Order]) -> List[Tuple[io.BytesIO, str]]:
        """Generate PDF buffers for each order with guaranteed fit."""
        order_buffers = []
        styles = getSampleStyleSheet()
        titulo_style = ParagraphStyle(
            name='TituloPedido',
            parent=styles['Heading2'],
            fontSize=18
        )

        for order_sn, order in orders.items():
            if not order.products:
                continue
                
            # Usar o order_sn do pedido, que já foi formatado corretamente
            buffer, orientation = self._try_fit_order(order.order_sn, order, titulo_style)
            order_buffers.append((buffer, orientation))
            
        return order_buffers

        return order_buffers

    def _generate_orders_pdf(self, order_buffers: List[Tuple[io.BytesIO, str]],
                           output_path: str, separate_last: bool) -> None:
        """Generate PDF with order pages"""
        # Calculate positions for orders on page
        positions = [
            (0, self.a4_height / 2),
            (self.a4_width / 2, self.a4_height / 2),
            (0, 0),
            (self.a4_width / 2, 0)
        ]

        # Handle separation of last orders if needed
        # Only separate if:
        # 1. separate_last is True (user selected the option)
        # 2. There are 1 or 2 orders on the last page (total_orders % 4 is 1 or 2)
        # 3. Total orders is not 3, 4, 7, 8, 11, 12, etc. (no separation if last page has 3 or 4 orders)
        total_orders = len(order_buffers)
        orders_on_last_page = total_orders % 4
        
        # Check if we should separate the last 1-2 orders
        should_separate = (
            separate_last and 
            orders_on_last_page in [1, 2] and  # Only 1 or 2 orders on last page
            total_orders > 2  # More than 2 total orders (so we're not separating all orders)
        )
        
        if should_separate:
            main_buffers = order_buffers[:-orders_on_last_page]
            separated_buffers = order_buffers[-orders_on_last_page:]
        else:
            main_buffers = order_buffers
            separated_buffers = []

        # Generate main PDF
        c = canvas.Canvas(output_path, pagesize=A4)
        self._draw_orders_to_canvas(c, main_buffers, positions)
        c.save()

        # Generate separated orders if needed
        if separated_buffers:
            # Save separated orders in the same directory as the main output
            output_dir = os.path.dirname(output_path)
            separated_filename = os.path.basename(output_path).replace('.pdf', '-separados.pdf')
            separated_path = os.path.join(os.getcwd(), separated_filename)
            self._generate_separated_orders_pdf(separated_buffers, separated_path)

    def _draw_orders_to_canvas(self, c: canvas.Canvas, order_buffers: List[Tuple[io.BytesIO, str]],
                             positions: List[Tuple[float, float]], center: bool = False) -> None:
        """Draw orders on canvas in specified positions"""
        if not order_buffers:
            return
            
        quadrant_width = self.a4_width / 2
        quadrant_height = self.a4_height / 2

        # Se estiver no modo center, ajustar as posições
        if center and positions:
            center_pos = positions[0]
            
            # Para 1 ou 2 pedidos, centralizar de verdade
            if len(order_buffers) == 1:
                # 1 pedido: centro superior
                positions = [(self.a4_width/2, self.a4_height * 0.75)]
            elif len(order_buffers) == 2:
                # 2 pedidos: centro superior e centro inferior
                positions = [
                    (self.a4_width/2, self.a4_height * 0.75),  # Centro superior
                    (self.a4_width/2, self.a4_height * 0.25)   # Centro inferior
                ]
            else:
                # 3 ou mais pedidos: usar o esquema de quadrantes centralizado
                positions = [
                    center_pos,  # Superior esquerdo
                    (center_pos[0] + self.a4_width/2, center_pos[1]),  # Superior direito
                    (center_pos[0], center_pos[1] - self.a4_height/2),  # Inferior esquerdo
                    (center_pos[0] + self.a4_width/2, center_pos[1] - self.a4_height/2)  # Inferior direito
                ]

        for i in range(0, len(order_buffers), 4):
            page_buffers = order_buffers[i:i + 4]
            
            # Determinar ordem e posições baseado no número de pedidos
            if len(page_buffers) == 4:
                order_indices = [0, 1, 2, 3]  # 1º, 2º, 3º, 4º pedido
                pos_indices = [1, 3, 0, 2]    # Posições: sup.dir, inf.dir, sup.esq, inf.esq
            elif len(page_buffers) == 3:
                order_indices = [0, 1, 2]     # 1º, 2º, 3º pedido
                pos_indices = [1, 3, 0]       # Posições: sup.dir, inf.dir, sup.esq
            elif len(page_buffers) == 2:
                order_indices = [0, 1]        # 1º, 2º pedido
                pos_indices = [0, 1]          # Usar as posições já calculadas (centro superior e inferior)
            elif len(page_buffers) == 1:
                order_indices = [0]           # 1º pedido
                pos_indices = [0]             # Usar a posição central
            else:
                continue

            # Draw orders in specified positions
            for idx, pos_idx in zip(order_indices, pos_indices):
                if idx >= len(page_buffers) or pos_idx >= len(positions):
                    continue

                buffer, orientation = page_buffers[idx]
                if buffer is None:
                    continue

                buffer, orientation = page_buffers[idx]
                if buffer is None:
                    continue

                # Convert PDF to image
                try:
                    doc = fitz.open(stream=buffer.getvalue(), filetype="pdf")
                    page = doc.load_page(0)
                    pix = page.get_pixmap()
                    img_data = pix.tobytes("png")
                    img = Image.open(io.BytesIO(img_data))

                    # Rotate if landscape
                    if orientation == 'landscape':
                        img = img.rotate(-90, expand=True)

                    # Save as PNG
                    img_buffer = io.BytesIO()
                    img.save(img_buffer, format="PNG")
                    img_buffer.seek(0)


                    # Calculate position
                    pos = positions[pos_idx]
                    if center:
                        pos = (pos[0] + self.a4_width/4, pos[1])

                        
                    # Draw on canvas
                    c.drawImage(ImageReader(img_buffer), pos[0], pos[1],
                              width=quadrant_width, height=quadrant_height)
                              
                except Exception as e:
                    print(f"Erro ao processar pedido: {e}")
                    continue

            c.showPage()

    def _generate_combined_pdf(self, order_buffers: List[Tuple[io.BytesIO, str]],
                             output_path: str, external_pdf: str) -> None:
        """Generate PDF combining orders with external PDF"""
        # Ensure output path is in the current working directory
        filename = os.path.basename(output_path)
        output_path = os.path.join(os.getcwd(), filename)
        
        # Generate orders PDF in memory
        orders_buffer = io.BytesIO()
        self._generate_orders_pdf(order_buffers, orders_buffer, False)
        orders_buffer.seek(0)

        # Open PDFs
        pdf_orders = fitz.open(stream=orders_buffer.getvalue(), filetype="pdf")
        pdf_external = fitz.open(external_pdf)
        pdf_result = fitz.open()

        # Combine PDFs alternating pages
        max_pages = max(pdf_external.page_count, pdf_orders.page_count * 2)
        for i in range(max_pages):
            if i % 2 == 0:  # External PDF pages (odd physical pages)
                idx_external = i // 2
                if idx_external < pdf_external.page_count:
                    pdf_result.insert_pdf(pdf_external, from_page=idx_external, to_page=idx_external)
                else:
                    pdf_result.new_page()
            else:  # Order pages (even physical pages)
                idx_orders = i // 2
                if idx_orders < pdf_orders.page_count:
                    pdf_result.insert_pdf(pdf_orders, from_page=idx_orders, to_page=idx_orders)
                else:
                    pdf_result.new_page()

        # Save and close
        pdf_result.save(output_path)
        pdf_orders.close()
        pdf_external.close()
        pdf_result.close()

    def _generate_combined_pdf_without_last(self, order_buffers: List[Tuple[io.BytesIO, str]],
                                          output_path: str, external_pdf: str) -> None:
        """
        Generate PDF combining orders with external PDF, excluding:
        - The last page of the external PDF
        - The last 1-2 orders (which will be moved to a separate PDF)
        
        Args:
            order_buffers: List of order buffers to include
            output_path: Path where the main PDF will be saved (filename only, will be saved in current directory)
            external_pdf: Path to the external PDF to include
        """
        # Ensure output path is in the current working directory
        filename = os.path.basename(output_path)
        output_path = os.path.join(os.getcwd(), filename)
        
        # Calculate how many orders to exclude (1 or 2)
        should_separate, orders_on_last_page = self._should_separate_last_orders(order_buffers)
        
        if should_separate:
            # Separate the last 1-2 orders
            main_order_buffers = order_buffers[:-orders_on_last_page]
            separated_order_buffers = order_buffers[-orders_on_last_page:]
            
            # Generate the separated PDF with the excluded orders
            separated_filename = os.path.basename(output_path).replace('.pdf', '-separados.pdf')
            separated_output_path = os.path.join(os.getcwd(), separated_filename)
            self._generate_separated_orders_pdf(separated_order_buffers, separated_output_path, external_pdf)
        else:
            # If we shouldn't separate, just use the combined PDF approach
            self._generate_combined_pdf(order_buffers, output_path, external_pdf)
            return
        
        # Generate the main PDF with the remaining orders
        if main_order_buffers:
            # Generate orders PDF in memory with the remaining orders
            orders_buffer = io.BytesIO()
            self._generate_orders_pdf(main_order_buffers, orders_buffer, False)
            orders_buffer.seek(0)
            
            pdf_orders = fitz.open(stream=orders_buffer.getvalue(), filetype="pdf")
            pdf_external = fitz.open(external_pdf)
            pdf_result = fitz.open()
            
            try:
                # Exclude the last page of the external PDF
                num_external_pages = max(0, pdf_external.page_count - 1)
                max_pages = max(num_external_pages * 2, pdf_orders.page_count * 2)
                
                # Combine the PDFs, alternating between external and order pages
                for i in range(max_pages):
                    if i % 2 == 0:  # External PDF page
                        idx_external = i // 2
                        if idx_external < num_external_pages:
                            pdf_result.insert_pdf(pdf_external, from_page=idx_external, to_page=idx_external)
                        else:
                            pdf_result.new_page()
                    else:  # Order page
                        idx_orders = i // 2
                        if idx_orders < pdf_orders.page_count:
                            pdf_result.insert_pdf(pdf_orders, from_page=idx_orders, to_page=idx_orders)
                        else:
                            pdf_result.new_page()
                
                # Save the result
                pdf_result.save(output_path)
                
            finally:
                # Ensure all resources are properly closed
                pdf_orders.close()
                pdf_external.close()
                pdf_result.close()
        else:
            # If there are no orders left for the main PDF, create an empty PDF
            with fitz.open() as doc:
                doc.save(output_path)
                
            # Still generate the separated PDF if we have orders to separate
            if separated_order_buffers:
                self._generate_separated_orders_pdf(separated_order_buffers, 
                                                   output_path.replace('.pdf', '-separados.pdf'), 
                                                   external_pdf)

    def _generate_separated_orders_pdf(self, order_buffers: List[Tuple[io.BytesIO, str]],
                                     output_path: str, external_pdf: Optional[str] = None) -> None:
        """
        Generate PDF for separated orders.
        - External PDF (Shipping Label): The left half of its last page is centered horizontally on a new A4 page.
        - Internal Orders PDF: Added exactly as they would be in the main PDF.
        
        Args:
            order_buffers: List of order buffers to include
            output_path: Path where the PDF will be saved (filename only, will be saved in current directory)
            external_pdf: Optional path to an external PDF to include
        """
        # Ensure output path is in the current working directory
        filename = os.path.basename(output_path)
        output_path = os.path.join(os.getcwd(), filename)
        
        pdf_result = fitz.open()  # This will be our final output PDF

        try:
            # Part 1: Process the external PDF (e.g., shipping label)
            if external_pdf and os.path.exists(external_pdf):
                try:
                    doc_ext = fitz.open(external_pdf)
                    if doc_ext.page_count > 0:
                        last_page_ext = doc_ext.load_page(doc_ext.page_count - 1)  # 0-indexed
                        
                        # Define the rectangle for the LEFT half of the source external page
                        half_width = last_page_ext.rect.width / 2
                        clip_rect_ext = fitz.Rect(
                            0,  # Start from left
                            0, 
                            half_width,  # Half width of the page
                            last_page_ext.rect.height
                        )
                        
                        page_result_ext = pdf_result.new_page(width=self.a4_width, height=self.a4_height)
                        
                        # Calculate position to center the clipped content
                        target_x_ext = (self.a4_width - clip_rect_ext.width) / 2
                        target_rect_ext = fitz.Rect(
                            target_x_ext, 
                            0,
                            target_x_ext + clip_rect_ext.width,
                            clip_rect_ext.height
                        )

                        page_result_ext.show_pdf_page(
                            target_rect_ext, 
                            doc_ext, 
                            last_page_ext.number, 
                            clip=clip_rect_ext
                        )
                    doc_ext.close()
                except Exception as e:
                    print(f"Error processing external PDF for separated orders: {e}")
                    # Continue processing orders even if external PDF fails

            # Part 2: Add orders exactly as they would be in the main PDF
            if order_buffers:
                try:
                    # If there's no external PDF, use _draw_orders_to_canvas with center=True
                    if not external_pdf or not os.path.exists(external_pdf):
                        # Create a temporary PDF with the orders using _draw_orders_to_canvas
                        temp_orders_buffer = io.BytesIO()
                        c = canvas.Canvas(temp_orders_buffer, pagesize=A4)
                        
                        # Define positions for the orders (same as in _draw_orders_to_canvas)
                        positions = [
                            (0, self.a4_height / 2),  # Top-left (3rd order)
                            (self.a4_width / 2, self.a4_height / 2),  # Top-right (1st order)
                            (0, 0),  # Bottom-left (4th order)
                            (self.a4_width / 2, 0)  # Bottom-right (2nd order)
                        ]
                        
                        # Draw orders with center=True to center them on the page
                        self._draw_orders_to_canvas(c, order_buffers, positions, center=True)
                        c.save()
                        temp_orders_buffer.seek(0)
                        
                        # Add the centered orders to the result PDF
                        doc_orders = fitz.open(stream=temp_orders_buffer.getvalue(), filetype="pdf")
                        pdf_result.insert_pdf(doc_orders)
                        doc_orders.close()
                        temp_orders_buffer.close()
                    else:
                        # Original behavior for when there is an external PDF
                        temp_orders_buffer = io.BytesIO()
                        c = canvas.Canvas(temp_orders_buffer, pagesize=A4)
                        
                        positions = [
                            (0, self.a4_height / 2),  # Top-left (3rd order)
                            (self.a4_width / 2, self.a4_height / 2),  # Top-right (1st order)
                            (0, 0),  # Bottom-left (4th order)
                            (self.a4_width / 2, 0)  # Bottom-right (2nd order)
                        ]
                        
                        # Draw each order in its correct position
                        for i, (buffer, orientation) in enumerate(order_buffers):
                            if i >= 4:  # Safety check, should not happen
                                break
                                
                            try:
                                # Determine the correct position based on the order index
                                pos_index = [1, 3, 0, 2][i] if i < 4 else i % 4
                                pos = positions[pos_index]
                                
                                # Convert the PDF page to an image
                                doc = fitz.open(stream=buffer.getvalue(), filetype="pdf")
                                page = doc.load_page(0)
                                
                                # Get the page as an image
                                pix = page.get_pixmap()
                                img_data = pix.tobytes("png")
                                img = Image.open(io.BytesIO(img_data))


                                # Rotate if needed based on orientation
                                if orientation == 'landscape':
                                    img = img.rotate(-90, expand=True)
                                
                                # Save as PNG
                                img_buffer = io.BytesIO()
                                img.save(img_buffer, format="PNG")
                                img_buffer.seek(0)
                                
                                # Draw on canvas at the calculated position
                                c.drawImage(ImageReader(img_buffer), 
                                         pos[0], 
                                         pos[1],
                                         width=self.a4_width/2, 
                                         height=self.a4_height/2)
                                
                                # Close resources
                                img.close()
                                doc.close()
                                
                            except Exception as e:
                                print(f"Error drawing order {i}: {e}")
                                continue
                        
                        # Save the canvas
                        c.showPage()
                        c.save()
                        temp_orders_buffer.seek(0)
                        
                        # Open the temporary PDF to crop the right half
                        doc_orders = fitz.open(stream=temp_orders_buffer.getvalue(), filetype="pdf")
                        
                        if doc_orders.page_count > 0:
                            # Get the first (and only) page
                            page = doc_orders.load_page(0)
                            
                            # Define the rectangle for the right half of the page
                            clip_rect = fitz.Rect(
                                page.rect.width / 2,  # Start from middle (right half)
                                0,
                                page.rect.width,      # Full width of the right half
                                page.rect.height
                            )
                            
                            # Create a new page in the result PDF
                            result_page = pdf_result.new_page(width=self.a4_width, height=self.a4_height)
                            
                            # Calculate position to center the right half
                            target_x = (self.a4_width - clip_rect.width) / 2
                            target_rect = fitz.Rect(
                                target_x,
                                0,
                                target_x + clip_rect.width,
                                clip_rect.height
                            )
                            
                            # Draw the right half of the page, centered on the new page
                            result_page.show_pdf_page(target_rect, doc_orders, page.number, clip=clip_rect)
                        
                        # Close resources
                        doc_orders.close()
                        temp_orders_buffer.close()
                
                except Exception as e:
                    print(f"Error processing order buffers for separated orders: {e}")
                    # Continue to save what we have even if there was an error with orders

            # Save the result if we have any pages
            if pdf_result.page_count > 0:
                pdf_result.save(output_path)
                print(f"Successfully saved separated orders PDF to: {output_path}")
            else:
                print(f"Warning: No pages were added to the separated PDF. Output file '{output_path}' might be empty or not created as expected.")
                
        except Exception as e:
            print(f"Unexpected error in _generate_separated_orders_pdf: {e}")
            raise
            
        finally:
            # Ensure all resources are properly closed
            pdf_result.close()
