#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Order processing service for Shopee orders
"""

import re
import pandas as pd
from typing import List, Tuple, Dict
from datetime import datetime
from models.order import Order, Product

class OrderProcessor:
    def __init__(self, excel_path: str):
        self.excel_path = excel_path
        self.orders: Dict[str, Order] = {}
        self.df = None

    def load_orders(self) -> bool:
        """Load orders from Excel file"""
        try:
            # Limpar dados existentes antes de carregar novos
            self.df = None
            self.orders = {}
            
            # Carregar dados do arquivo Excel
            self.df = pd.read_excel(self.excel_path)
            if 'product_info' not in self.df.columns or 'order_sn' not in self.df.columns:
                raise ValueError("Invalid Excel format: missing required columns")
                
            # Forçar leitura de todos os dados
            self.df = self.df.copy()
            return True
        except Exception as e:
            self.df = None
            self.orders = {}
            raise ValueError(f"Error loading Excel file: {str(e)}")

    def process_orders(self) -> Dict[str, Order]:
        """Process all orders from the loaded Excel file"""
        if self.df is None:
            raise ValueError("No data loaded. Call load_orders() first.")
            
        # Limpar pedidos existentes
        self.orders = {}

        # Processar cada linha do DataFrame
        for _, row in self.df.iterrows():
            try:
                if pd.notna(row['product_info']):
                    order = Order.from_product_info(
                        str(row['order_sn']).strip(),
                        str(row['product_info'])
                    )
                    self.orders[order.order_sn] = order
            except Exception as e:
                print(f"Error processing order {row.get('order_sn', 'UNKNOWN')}: {str(e)}")
                continue

        return self.orders

    def get_separation_report_data(self) -> List[List[str]]:
        """Generate separation report data"""
        if not self.orders:
            raise ValueError("No orders processed. Call process_orders() first.")

        # Collect all products across all orders
        products_dict = {}
        for order in self.orders.values():
            for product in order.products:
                key = (product.sku, product.name, product.variation)
                if key in products_dict:
                    products_dict[key] += product.quantity
                else:
                    products_dict[key] = product.quantity

        # Sort products by SKU
        sorted_products = sorted(products_dict.items(), key=lambda x: x[0][0])

        # Format data for table
        data = [["SKU", "QNT", "NOME", "VARIAÇÃO"]]
        for (sku, name, variation), quantity in sorted_products:
            data.append([sku, quantity, name, variation])

        return data

    @staticmethod
    def extract_product_info(product_info: str) -> List[Tuple[str, str, str, int]]:
        """Extract product information from formatted string"""
        pattern = r"Product Name:(.*?); Variation Name:(.*?); Price:.*?; Quantity:\s*(\d+); SKU Reference No\.: (\d+)"
        product_info = re.sub(r"\s+", " ", product_info.strip())
        matches = re.findall(pattern, product_info)

        products = []
        for match in matches:
            nome_produto = match[0].strip()
            variacao_produto = match[1].strip()
            quantidade = int(match[2].strip())
            sku = match[3].strip()
            products.append((sku, nome_produto, variacao_produto, quantidade))

        return products

    def get_output_directory(self) -> str:
        """Get the output directory for generated files"""
        import os
        # Sempre usar o diretório de execução do script
        return os.getcwd()

    def generate_filenames(self) -> Tuple[str, str, str]:
        """Generate filenames for output PDFs"""
        from config import Config
        import os
        
        output_dir = self.get_output_directory()
        date_str = datetime.today().strftime('%d%m%y-%H%M')
        
        separation_file = os.path.join(
            output_dir, 
            Config.OUTPUT_FILENAMES['separation'].format(date=date_str)
        )
        orders_file = os.path.join(
            output_dir,
            Config.OUTPUT_FILENAMES['orders'].format(date=date_str)
        )
        separated_orders_file = os.path.join(
            output_dir,
            Config.OUTPUT_FILENAMES['separated_orders'].format(date=date_str)
        )
        
        return separation_file, orders_file, separated_orders_file
