#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration module for the Shopee Order Processor
"""

class Config:
    # PDF Settings
    PDF_MARGINS = {
        'top': 15,
        'bottom': 15,
        'left': 15,
        'right': 15
    }
    
    # Table Settings
    TABLE_STYLES = {
        'header_font_size': 12,
        'max_font_size': 18,
        'min_font_size': 6,  # Reduced from 14 to 6 to allow smaller text when needed
        'line_height_multiplier': 1.3
    }
    
    # File Naming
    OUTPUT_FILENAMES = {
        'separation': "Separacao-{date}.pdf",
        'orders': "Pedidos-{date}.pdf",
        'separated_orders': "Pedidos-Separados-{date}.pdf"
    }
    
    # Dependencies
    REQUIRED_PACKAGES = {
        'pandas': 'pandas>=1.3.0',
        'reportlab': 'reportlab>=3.6.0',
        'PyMuPDF': 'PyMuPDF>=1.19.0',
        'Pillow': 'Pillow>=8.3.0',
        'openpyxl': 'openpyxl>=3.0.7',
        'importlib-metadata': 'importlib-metadata>=4.6.0'
    }
    
    # PDF Layout
    PDF_LAYOUT = {
        'orders_per_page': 4,
        'quadrant_width_factor': 2,  # A4_WIDTH / 2
        'quadrant_height_factor': 2,  # A4_HEIGHT / 2
    }
